<template>
  <view>
    <view class="h-screen bg-gray-50 overflow-hidden" v-if="!loading">
      <view>
        <!-- 群组设置 -->
        <view class="bg-white rounded-xl shadow-sm mb-4 mx-4 mt-4">
          <!-- 我的群昵称 (所有用户) -->
          <view class="border-b border-gray-50" @click="editNickname">
            <view class="flex items-center p-4">
              <i class="fas fa-user-edit text-green-500 w-6 mr-3"></i>
              <view class="flex-1">
                <text class="text-base text-gray-800 block">我的群昵称</text>
                <text class="text-sm text-gray-500">{{
                  groupInfo.nickname || "未设置"
                }}</text>
              </view>
              <i class="fas fa-chevron-right text-gray-300"></i>
            </view>
          </view>

          <template v-if="isAdmin">
            <!-- 成员管理 (仅管理员) -->
            <view class="border-b border-gray-50" @click="goToMembers">
              <view class="flex items-center p-4">
                <i class="fas fa-users text-blue-500 w-6 mr-3"></i>
                <view class="flex-1">
                  <text class="text-base text-gray-800 block">成员管理</text>
                  <text class="text-sm text-gray-500">查看和管理群成员</text>
                </view>
                <i class="fas fa-chevron-right text-gray-300"></i>
              </view>
            </view>

            <!-- 入群申请审核 (仅管理员，带角标) -->
            <view class="border-b border-gray-50" @click="goToApproval">
              <view class="flex items-center p-4">
                <i class="fas fa-user-check text-orange-500 w-6 mr-3"></i>
                <view class="flex-1">
                  <text class="text-base text-gray-800 block"
                    >入群申请审核</text
                  >
                  <text class="text-sm text-gray-500">审核待加入的成员</text>
                </view>
                <!-- 审核角标 -->
                <view
                  v-if="groupInfo.applyCount"
                  class="w-6 h-6 rounded-full bg-red-500 flex items-center justify-center mr-2"
                >
                  <text class="text-white text-xs">{{
                    groupInfo.applyCount > 99 ? "99+" : groupInfo.applyCount
                  }}</text>
                </view>
                <i class="fas fa-chevron-right text-gray-300"></i>
              </view>
            </view>

            <!-- 群头像 (仅管理员) -->
            <view class="border-b border-gray-50" @click="editGroupAvatar">
              <view class="flex items-center p-4">
                <i class="fas fa-image text-indigo-500 w-6 mr-3"></i>
                <view class="flex-1">
                  <text class="text-base text-gray-800 block">群头像</text>
                  <text class="text-sm text-gray-500">点击修改群头像</text>
                </view>
                <view class="mr-2">
                  <!-- <image
                  v-if="groupInfo.avatar"
                  :src="groupInfo.avatar"
                  class="w-full h-full rounded-lg"
                />
                <i v-else class="fas fa-users text-gray-400"></i> -->
                  <GroupAvatar :avatar="groupInfo.avatar" size="small" />
                </view>
                <i class="fas fa-chevron-right text-gray-300"></i>
              </view>
            </view>

            <!-- 群名称 (仅管理员) -->
            <view class="border-b border-gray-50" @click="editGroupName">
              <view class="flex items-center p-4">
                <i class="fas fa-edit text-blue-500 w-6 mr-3"></i>
                <view class="flex-1">
                  <text class="text-base text-gray-800 block">群名称</text>
                  <text class="text-sm text-gray-500">{{
                    groupInfo.name
                  }}</text>
                </view>
                <i class="fas fa-chevron-right text-gray-300"></i>
              </view>
            </view>

            <!-- 群介绍 (仅管理员) -->
            <view class="border-b border-gray-50" @click="editGroupDescription">
              <view class="flex items-center p-4">
                <i class="fas fa-align-left text-teal-500 w-6 mr-3"></i>
                <view class="flex-1">
                  <text class="text-base text-gray-800 block">群介绍</text>
                  <text class="text-sm text-gray-500">{{
                    groupInfo.description || "暂无群介绍"
                  }}</text>
                </view>
                <i class="fas fa-chevron-right text-gray-300"></i>
              </view>
            </view>

            <!-- 群公告 (仅管理员) -->
            <view class="border-b border-gray-50" @click="editGroupNotice">
              <view class="flex items-center p-4">
                <i class="fas fa-bullhorn text-orange-500 w-6 mr-3"></i>
                <view class="flex-1">
                  <text class="text-base text-gray-800 block">群公告</text>
                  <text class="text-sm text-gray-500">{{
                    groupInfo.notice || "暂无群公告"
                  }}</text>
                </view>
                <i class="fas fa-chevron-right text-gray-300"></i>
              </view>
            </view>

            <!-- 加入方式 (仅管理员) -->
            <view @click="editJoinType">
              <view class="flex items-center p-4">
                <i class="fas fa-door-open text-cyan-500 w-6 mr-3"></i>
                <view class="flex-1">
                  <text class="text-base text-gray-800 block">加入方式</text>
                  <text class="text-sm text-gray-500">{{
                    getJoinTypeText(groupInfo.joinType)
                  }}</text>
                </view>
                <i class="fas fa-chevron-right text-gray-300"></i>
              </view>
            </view>
          </template>
        </view>

        <!-- 解散/退出群组按钮 -->
        <view class="mx-4 mb-8">
          <button
            @click="confirmAction"
            class="w-full py-4 bg-white rounded-xl border border-red-200 text-red-600 font-medium text-base"
          >
            {{ isAdmin ? "解散群组" : "退出群组" }}
          </button>
        </view>
      </view>

      <!-- 修改群昵称弹窗 -->
      <view
        v-if="showNicknameModal"
        class="fixed inset-0 z-50 flex items-center justify-center"
        @click="closeNicknameModal"
      >
        <view class="absolute inset-0 bg-black bg-opacity-50"></view>
        <view class="bg-white rounded-xl p-6 mx-4 w-80 relative" @click.stop>
          <text class="text-lg font-semibold text-gray-800 block mb-4"
            >修改群昵称</text
          >
          <view class="relative mb-1">
            <input
              v-model="tempNickname"
              class="w-full h-auto p-3 pr-10 border text-sm border-gray-200 rounded-lg"
              placeholder="请输入群昵称（16字以内）"
              maxlength="16"
              auto-focus="true"
            />
            <view
              v-if="tempNickname.length > 0"
              @click="clearNickname"
              class="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 rounded-full bg-gray-300 flex items-center justify-center cursor-pointer"
            >
              <i class="fas fa-times text-white text-xs"></i>
            </view>
          </view>
          <view class="flex justify-end mb-4">
            <text class="text-xs text-gray-400"
              >{{ tempNickname.length }}/16</text
            >
          </view>
          <view class="flex space-x-3">
            <button
              @click="closeNicknameModal"
              class="flex-1 py-3 text-sm bg-gray-100 text-gray-600 rounded-lg"
            >
              取消
            </button>
            <button
              @click="saveNickname"
              class="flex-1 py-3 text-sm bg-primary-500 text-white rounded-lg"
            >
              确定
            </button>
          </view>
        </view>
      </view>

      <!-- 修改群名称弹窗 -->
      <view
        v-if="showGroupNameModal"
        class="fixed inset-0 z-50 flex items-center justify-center"
        @click="closeGroupNameModal"
      >
        <view class="absolute inset-0 bg-black bg-opacity-50"></view>
        <view class="bg-white rounded-xl p-6 mx-4 w-80 relative" @click.stop>
          <text class="text-lg font-semibold text-gray-800 block mb-4"
            >修改群名称</text
          >
          <view class="relative mb-1">
            <input
              v-model="tempGroupName"
              class="w-full h-auto p-3 pr-10 border border-gray-200 rounded-lg"
              placeholder="请输入群名称（16字以内）"
              maxlength="16"
              auto-focus="true"
            />
            <view
              v-if="tempGroupName.length > 0"
              @click="clearGroupName"
              class="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 rounded-full bg-gray-300 flex items-center justify-center cursor-pointer"
            >
              <i class="fas fa-times text-white text-xs"></i>
            </view>
          </view>
          <view class="flex justify-end mb-4">
            <text class="text-xs text-gray-400"
              >{{ tempGroupName.length }}/16</text
            >
          </view>
          <view class="flex space-x-3">
            <button
              @click="closeGroupNameModal"
              class="flex-1 py-3 text-sm bg-gray-100 text-gray-600 rounded-lg"
            >
              取消
            </button>
            <button
              @click="saveGroupName"
              class="flex-1 py-3 bg-primary-500 text-white rounded-lg"
            >
              确定
            </button>
          </view>
        </view>
      </view>

      <!-- 修改群头像弹窗 -->
      <view
        v-if="showAvatarModal"
        class="fixed inset-0 z-50 flex items-center justify-center"
        @click="closeAvatarModal"
      >
        <view class="absolute inset-0 bg-black bg-opacity-50"></view>
        <view class="bg-white rounded-xl p-6 mx-4 w-80 relative" @click.stop>
          <text class="text-lg font-semibold text-gray-800 block mb-4"
            >修改群头像</text
          >
          <view class="flex flex-col items-center">
            <AvatarSelector v-model="tempGroupAvatar" />
          </view>
          <view class="flex space-x-3 mt-4">
            <button
              @click="closeAvatarModal"
              class="flex-1 py-3 text-sm bg-gray-100 text-gray-600 rounded-lg"
            >
              取消
            </button>
            <button
              @click="saveGroupAvatar"
              class="flex-1 py-3 text-sm bg-primary-500 text-white rounded-lg"
            >
              确定
            </button>
          </view>
        </view>
      </view>

      <!-- 修改群介绍弹窗 -->
      <view
        v-if="showDescriptionModal"
        class="fixed inset-0 z-50 flex items-center justify-center"
        @click="closeDescriptionModal"
      >
        <view class="absolute inset-0 bg-black bg-opacity-50"></view>
        <view class="bg-white rounded-xl p-6 mx-4 w-80 relative" @click.stop>
          <text class="text-lg font-semibold text-gray-800 block mb-4"
            >修改群介绍</text
          >
          <view class="relative mb-1">
            <textarea
              v-model="tempDescription"
              class="w-full p-3 pr-10 border border-gray-200 rounded-lg h-24"
              placeholder="请输入群介绍（50字以内）"
              maxlength="50"
            ></textarea>
            <view
              v-if="tempDescription.length > 0"
              @click="clearDescription"
              class="absolute right-3 top-3 w-5 h-5 rounded-full bg-gray-300 flex items-center justify-center cursor-pointer"
            >
              <i class="fas fa-times text-white text-xs"></i>
            </view>
          </view>
          <view class="flex justify-end mb-4">
            <text class="text-xs text-gray-400"
              >{{ tempDescription.length }}/50</text
            >
          </view>
          <view class="flex space-x-3">
            <button
              @click="closeDescriptionModal"
              class="flex-1 py-3 text-sm bg-gray-100 text-gray-600 rounded-lg"
            >
              取消
            </button>
            <button
              @click="saveDescription"
              class="flex-1 py-3 text-sm bg-primary-500 text-white rounded-lg"
            >
              确定
            </button>
          </view>
        </view>
      </view>

      <!-- 修改群公告弹窗 -->
      <view
        v-if="showNoticeModal"
        class="fixed inset-0 z-50 flex items-center justify-center"
        @click="closeNoticeModal"
      >
        <view class="absolute inset-0 bg-black bg-opacity-50"></view>
        <view class="bg-white rounded-xl p-6 mx-4 w-80 relative" @click.stop>
          <text class="text-lg font-semibold text-gray-800 block mb-4"
            >设置群公告</text
          >
          <view class="relative mb-1">
            <textarea
              v-model="tempNotice"
              class="w-full p-3 pr-10 border border-gray-200 rounded-lg h-32"
              placeholder="请输入群公告（100字以内）"
              maxlength="100"
            ></textarea>
            <view
              v-if="tempNotice.length > 0"
              @click="clearNotice"
              class="absolute right-3 top-3 w-5 h-5 rounded-full bg-gray-300 flex items-center justify-center cursor-pointer"
            >
              <i class="fas fa-times text-white text-xs"></i>
            </view>
          </view>
          <view class="flex justify-end mb-4">
            <text class="text-xs text-gray-400"
              >{{ tempNotice.length }}/100</text
            >
          </view>
          <view class="flex space-x-3">
            <button
              @click="closeNoticeModal"
              class="flex-1 py-3 text-sm bg-gray-100 text-gray-600 rounded-lg"
            >
              取消
            </button>
            <button
              @click="saveNotice"
              class="flex-1 py-3 text-sm bg-primary-500 text-white rounded-lg"
            >
              确定
            </button>
          </view>
        </view>
      </view>

      <!-- 修改加入方式弹窗 -->
      <view
        v-if="showJoinTypeModal"
        class="fixed inset-0 z-50 flex items-center justify-center"
        @click="closeJoinTypeModal"
      >
        <view class="absolute inset-0 bg-black bg-opacity-50"></view>
        <view class="bg-white rounded-xl p-6 mx-4 w-80 relative" @click.stop>
          <text class="text-lg font-semibold text-gray-800 block mb-4"
            >修改加入方式</text
          >

          <!-- 加入方式选择 -->
          <view class="mb-4">
            <view
              v-for="option in joinTypeOptions"
              :key="option.value"
              @click="selectJoinType(option.value)"
              class="flex items-center p-3 border border-gray-200 rounded-lg mb-3"
              :class="
                tempJoinType === option.value
                  ? 'border-primary-500 bg-primary-50'
                  : ''
              "
            >
              <view
                class="w-5 h-5 rounded-full border-2 flex items-center justify-center mr-3"
                :class="
                  tempJoinType === option.value
                    ? 'border-primary-500'
                    : 'border-gray-300'
                "
              >
                <view
                  v-if="tempJoinType === option.value"
                  class="w-2.5 h-2.5 rounded-full bg-primary-500"
                ></view>
              </view>
              <view class="flex-1">
                <text class="text-base font-medium text-gray-800 block">{{
                  option.label
                }}</text>
                <text class="text-sm text-gray-500">{{ option.desc }}</text>
              </view>
            </view>
          </view>

          <!-- 口令设置 -->
          <view v-if="tempJoinType === 3" class="mb-4">
            <text class="text-base font-semibold text-gray-800 mb-2 block"
              >入群口令</text
            >
            <input
              v-model="tempJoinPassword"
              placeholder="请输入6位数字口令"
              maxlength="6"
              type="number"
              class="w-full px-3 h-auto py-2 border border-gray-200 rounded-lg text-base"
            />
          </view>

          <view class="flex space-x-3">
            <button
              @click="closeJoinTypeModal"
              class="flex-1 py-3 text-sm bg-gray-100 text-gray-600 rounded-lg"
            >
              取消
            </button>
            <button
              @click="saveJoinType"
              class="flex-1 py-3 text-sm bg-primary-500 text-white rounded-lg"
            >
              确定
            </button>
          </view>
        </view>
      </view>
    </view>
    <loading-view :show="loading" />
  </view>
</template>

<script>
import AvatarSelector from "@/components/AvatarSelector.vue";
import GroupAvatar from "@/components/GroupAvatar";

export default {
  components: {
    AvatarSelector,
    GroupAvatar,
  },
  data() {
    return {
      groupId: "",
      loading: true,
      groupInfo: {
        nickname: "",
        id: 1,
        name: "",
        description: "",
        avatar: "",
        notice: "",

        role: 3,
        joinType: 2,
        joinPassword: "", // 入群口令
        applyCount: 0,
        memberCount: 0,
      },

      // 弹窗控制
      showNicknameModal: false,
      showGroupNameModal: false,
      showAvatarModal: false,
      showDescriptionModal: false,
      showNoticeModal: false,
      showJoinTypeModal: false,

      // 临时数据
      tempNickname: "",
      tempGroupName: "",
      tempGroupAvatar: "fas fa-users", // 临时头像
      tempDescription: "",
      tempNotice: "",
      tempJoinType: 2,

      // 加入方式选项（参考 create.vue）
      joinTypeOptions: [
        { value: 1, label: "不限制", desc: "任何人都可以直接加入群组" },
        { value: 2, label: "审核后加入", desc: "需要群主或管理员审核通过" },
        { value: 3, label: "口令加入", desc: "需要输入正确的6位数字口令" },
      ],

      // 口令相关
      tempJoinPassword: "",
    };
  },
  computed: {
    isAdmin() {
      return this.groupInfo.role && this.groupInfo.role != 1;
    },
  },

  methods: {
    loadData() {
      this.$reqGet("/front/edu/group/detail", { id: this.groupId }).then(
        (res) => {
          this.loading = false;
          if (res.success) {
            this.groupInfo = res.data;
          } else {
            uni.showToast({
              title: res.errorMessage,
              icon: "none",
            });
          }
        }
      );
    },

    reqUpdate(data) {
      this.$reqPost("/front/edu/group/update", {
        id: this.groupId,
        ...data,
      }).then((res) => {
        if (res.success) {
          uni.showToast({
            title: "修改成功",
            icon: "success",
          });
          //直接修改groupInfo的值
          this.groupInfo = {
            ...this.groupInfo,
            ...data,
          };
          this.closeAvatarModal();
          this.closeDescriptionModal();
          this.closeGroupNameModal();
          this.closeJoinTypeModal();
          this.closeNoticeModal();
        } else {
          uni.showToast({
            title: res.errorMessage || "修改失败",
            icon: "none",
          });
        }
      });
    },

    getJoinTypeText(type) {
      const typeMap = {
        1: "不限制",
        2: "审核后加入",
        3: "口令加入",
      };
      return typeMap[type] || "未知";
    },

    // 弹窗相关方法
    editNickname() {
      this.tempNickname = this.groupInfo.nickname;
      this.showNicknameModal = true;
    },

    closeNicknameModal() {
      this.showNicknameModal = false;
      this.tempNickname = "";
    },

    clearNickname() {
      this.tempNickname = "";
    },

    saveNickname() {
      if (!this.tempNickname.trim()) {
        uni.showToast({
          title: "请输入群昵称",
          icon: "none",
        });
        return;
      }

      if (this.tempNickname.length > 16) {
        uni.showToast({
          title: "群昵称不能超过16个字符",
          icon: "none",
        });
        return;
      }

      this.$reqPost("/front/edu/group-member/update", {
        groupId: this.groupId,
        nickname: this.tempNickname.trim(),
      }).then((res) => {
        if (res.success) {
          this.groupInfo.nickname = this.tempNickname.trim();

          this.closeNicknameModal();
          uni.showToast({
            title: "昵称修改成功",
            icon: "success",
          });
        } else {
          uni.showToast({
            title: res.errorMessage || "修改失败",
            icon: "none",
          });
        }
      });
    },

    goToMembers() {
      uni.navigateTo({
        url: `/pages/group/members?groupId=${this.groupId}`,
      });
    },

    goToApproval() {
      uni.navigateTo({
        url: `/pages/group/approval?groupId=${this.groupId}`,
      });
    },

    editGroupName() {
      this.tempGroupName = this.groupInfo.name;
      this.showGroupNameModal = true;
    },

    closeGroupNameModal() {
      this.showGroupNameModal = false;
      this.tempGroupName = "";
    },

    clearGroupName() {
      this.tempGroupName = "";
    },

    saveGroupName() {
      if (!this.tempGroupName.trim()) {
        uni.showToast({
          title: "请输入群名称",
          icon: "none",
        });
        return;
      }

      if (this.tempGroupName.length > 16) {
        uni.showToast({
          title: "群名称不能超过16个字符",
          icon: "none",
        });
        return;
      }

      this.reqUpdate({ name: this.tempGroupName.trim() });
    },

    editGroupAvatar() {
      this.tempGroupAvatar = this.groupInfo.avatar || "fas fa-users";
      console.log(this.tempGroupAvatar);
      this.showAvatarModal = true;
    },

    closeAvatarModal() {
      this.showAvatarModal = false;
      this.tempGroupAvatar = "fas fa-users";
    },

    saveGroupAvatar() {
      this.reqUpdate({ avatar: this.tempGroupAvatar });
    },

    editGroupDescription() {
      this.tempDescription = this.groupInfo.description || "";
      this.showDescriptionModal = true;
    },

    closeDescriptionModal() {
      this.showDescriptionModal = false;
      this.tempDescription = "";
    },

    clearDescription() {
      this.tempDescription = "";
    },

    saveDescription() {
      if (!this.tempDescription.trim()) {
        uni.showToast({
          title: "请输入群介绍",
          icon: "none",
        });
        return;
      }
      if (this.tempDescription.length > 50) {
        uni.showToast({
          title: "群介绍不能超过50个字符",
          icon: "none",
        });
        return;
      }

      this.reqUpdate({ description: this.tempDescription.trim() });
    },

    editGroupNotice() {
      this.tempNotice = this.groupInfo.notice || "";
      this.showNoticeModal = true;
    },

    closeNoticeModal() {
      this.showNoticeModal = false;
      this.tempNotice = "";
    },

    clearNotice() {
      this.tempNotice = "";
    },

    saveNotice() {
      if (this.tempNotice.length > 100) {
        uni.showToast({
          title: "群公告不能超过100个字符",
          icon: "none",
        });
        return;
      }

      this.reqUpdate({ notice: this.tempNotice.trim() });
    },

    editJoinType() {
      this.tempJoinType = this.groupInfo.joinType;
      this.tempJoinPassword = this.groupInfo.joinPassword || "";
      this.showJoinTypeModal = true;
    },

    closeJoinTypeModal() {
      this.showJoinTypeModal = false;
      this.tempJoinType = this.groupInfo.joinType;
      this.tempJoinPassword = "";
    },

    selectJoinType(type) {
      this.tempJoinType = type;
      // 如果不是口令加入，清空口令
      if (type !== 3) {
        this.tempJoinPassword = "";
      }
    },

    saveJoinType() {
      // 如果是口令加入，验证口令
      if (this.tempJoinType === 3) {
        if (!this.tempJoinPassword) {
          uni.showToast({
            title: "请输入口令",
            icon: "none",
          });
          return;
        }

        if (!/^\d{6}$/.test(this.tempJoinPassword)) {
          uni.showToast({
            title: "口令必须是6位数字",
            icon: "none",
          });
          return;
        }
      }

      this.reqUpdate({
        joinType: this.tempJoinType,
        joinPassword: this.tempJoinPassword,
      });
    },

    confirmAction() {
      const title = this.isAdmin ? "确认解散" : "确认退出";
      console.log(this.isAdmin);
      const content = this.isAdmin
        ? "解散后群组将被永久删除，且无法恢复，确定要解散吗？"
        : "确定要退出该群组吗？退出后将无法使用该群组功能。";

      uni.showModal({
        title: title,
        content: content,
        confirmColor: "#ef4444",
        success: (res) => {
          if (res.confirm) {
            this.$reqGet("/front/edu/group/exit", { id: this.groupId }).then(
              (res) => {
                if (res.success) {
                  uni.showModal({
                    title: "操作成功",
                    content: `您已成功${
                      this.isAdmin ? "解散群组" : "退出群组"
                    }`,
                    showCancel: false,
                    success: (res) => {
                      if (res.confirm) {
                        uni.navigateBack({ delta: 2 });
                      }
                    },
                  });
                } else {
                  uni.showToast({
                    title: res.errorMessage || "操作失败",
                    icon: "none",
                  });
                }
              }
            );
          }
        },
      });
    },
  },

  onLoad(options) {
    this.groupId = options.id;
    this.loadData();
  },
};
</script>

<style scoped></style>
